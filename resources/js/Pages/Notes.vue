<script setup>
import { ref, onMounted } from 'vue'
import { Head, Link } from '@inertiajs/vue3'

const notes = ref([])
const editingId = ref(null)

const form = ref({
  bean_name: '',
  origin: '',
  roast_level: '',
  flavor_notes: '',
  rating: null,
  brewing_method: ''
})

// 獲取 CSRF token
const getCsrfToken = () => {
  return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
}

const fetchNotes = async () => {
  try {
    const res = await fetch('/api/notes', {
      headers: {
        'Accept': 'application/json',
        'X-CSRF-TOKEN': getCsrfToken(),
        'X-Requested-With': 'XMLHttpRequest'
      },
      credentials: 'same-origin'
    })
    if (res.ok) {
      const data = await res.json()
      notes.value = data
    } else {
      console.error('獲取筆記失敗:', res.status, res.statusText)
      notes.value = []
    }
  } catch (error) {
    console.error('獲取筆記失敗:', error)
    notes.value = []
  }
}

const submitNote = async () => {
  try {
    const url = editingId.value
      ? `/api/notes/${editingId.value}`
      : '/api/notes'

    const method = editingId.value ? 'PATCH' : 'POST'

    const res = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-CSRF-TOKEN': getCsrfToken(),
        'X-Requested-With': 'XMLHttpRequest'
      },
      credentials: 'same-origin',
      body: JSON.stringify(form.value)
    })

    if (res.ok) {
      form.value = {
        bean_name: '',
        origin: '',
        roast_level: '',
        flavor_notes: '',
        rating: null,
        brewing_method: ''
      }
      editingId.value = null
      fetchNotes()
    } else {
      console.error('提交失敗:', res.status, res.statusText)
    }
  } catch (error) {
    console.error('提交筆記失敗:', error)
  }
}

const editNote = (note) => {
  editingId.value = note.id
  form.value = { ...note }
}

const deleteNote = async (id) => {
  if (!confirm('確定要刪除這筆筆記嗎？')) return
  try {
    const res = await fetch(`/api/notes/${id}`, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json',
        'X-CSRF-TOKEN': getCsrfToken(),
        'X-Requested-With': 'XMLHttpRequest'
      },
      credentials: 'same-origin'
    })
    if (res.ok) {
      fetchNotes()
    } else {
      console.error('刪除失敗:', res.status, res.statusText)
    }
  } catch (error) {
    console.error('刪除筆記失敗:', error)
  }
}

onMounted(fetchNotes)
</script>

<template>
  <Head title="Coffee Notes" />
  <div class="p-4 max-w-xl mx-auto">
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-2xl font-bold">☕ Coffee Journal</h1>
      <Link href="/dashboard" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
        前往 Dashboard
      </Link>
    </div>

    <!-- 表單 -->
    <form @submit.prevent="submitNote" class="space-y-2 mb-6">
      <input v-model="form.bean_name" placeholder="豆子名稱" required class="border p-2 w-full" />
      <input v-model="form.origin" placeholder="產地" class="border p-2 w-full" />
      <input v-model="form.roast_level" placeholder="烘焙程度" required class="border p-2 w-full" />
      <input v-model="form.flavor_notes" placeholder="風味" class="border p-2 w-full" />
      <input v-model.number="form.rating" type="number" placeholder="評分 1~5" class="border p-2 w-full" min="1" max="5" />
      <input v-model="form.brewing_method" placeholder="沖煮法" class="border p-2 w-full" />
      <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded">
        {{ editingId ? '更新筆記' : '新增筆記' }}
      </button>
      <button v-if="editingId" @click="editingId = null" type="button" class="ml-2 text-gray-500 underline">
        取消編輯
      </button>
    </form>

    <!-- 筆記清單 -->
    <div v-if="notes.length">
      <h2 class="text-lg font-semibold mb-2">筆記列表：</h2>
      <ul>
        <li v-for="note in notes" :key="note.id" class="border-b py-2 space-y-1">
          <div><strong>{{ note.bean_name }}</strong>（{{ note.origin || '未知產地' }}）</div>
          <div>烘焙：{{ note.roast_level }}｜評分：{{ note.rating || '-' }}/5</div>
          <div>風味：{{ note.flavor_notes }}</div>
          <div>沖煮法：{{ note.brewing_method }}</div>
          <div class="space-x-2 text-sm mt-1">
            <button @click="editNote(note)" class="text-blue-600">編輯</button>
            <button @click="deleteNote(note.id)" class="text-red-600">刪除</button>
          </div>
        </li>
      </ul>
    </div>

    <div v-else class="text-gray-500">目前尚無筆記</div>
  </div>
</template>
