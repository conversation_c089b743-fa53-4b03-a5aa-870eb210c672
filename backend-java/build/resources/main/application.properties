# 應用程式配置
spring.application.name=coffeenote-api
server.port=8080

# 資料庫配置
# 開發環境使用 H2 記憶體資料庫
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# 生產環境使用 PostgreSQL（註解掉開發時使用）
# spring.datasource.url=*******************************************
# spring.datasource.username=postgres
# spring.datasource.password=your_password
# spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate 配置
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# H2 控制台配置（僅開發環境）
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JWT 配置
jwt.secret=mySecretKey12345678901234567890123456789012345678901234567890
jwt.expiration=86400000

# 日誌配置
logging.level.com.coffeenote.api=DEBUG
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# 跨域配置
spring.web.cors.allowed-origins=*
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
