[2025-07-09 05:25:20] local.ERROR: SQLSTATE[42P07]: Duplicate table: 7 ERROR:  relation "coffee_notes" already exists (Connection: pgsql, SQL: create table "coffee_notes" ("id" bigserial not null primary key, "bean_name" varchar(255) not null, "origin" varchar(255) null, "roast_level" varchar(255) not null, "flavor_notes" text null, "rating" integer null, "brewing_method" varchar(255) null, "created_at" timestamp(0) without time zone null, "updated_at" timestamp(0) without time zone null)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P07): SQLSTATE[42P07]: Duplicate table: 7 ERROR:  relation \"coffee_notes\" already exists (Connection: pgsql, SQL: create table \"coffee_notes\" (\"id\" bigserial not null primary key, \"bean_name\" varchar(255) not null, \"origin\" varchar(255) null, \"roast_level\" varchar(255) not null, \"flavor_notes\" text null, \"rating\" integer null, \"brewing_method\" varchar(255) null, \"created_at\" timestamp(0) without time zone null, \"updated_at\" timestamp(0) without time zone null)) at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(118): Illuminate\\Database\\Connection->statement()
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(418): Illuminate\\Database\\Schema\\Builder->build()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 /home/<USER>/coffee-journal/database/migrations/2025_07_09_052427_create_coffee_notes_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php(32): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(440): Illuminate\\Database\\Connection->transaction()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render()
#14 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#27 /home/<USER>/coffee-journal/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#29 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run()
#30 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#31 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#32 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#33 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 /home/<USER>/coffee-journal/artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#35 {main}

[previous exception] [object] (PDOException(code: 42P07): SQLSTATE[42P07]: Duplicate table: 7 ERROR:  relation \"coffee_notes\" already exists at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php:571)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(571): PDOStatement->execute()
#1 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(118): Illuminate\\Database\\Connection->statement()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build()
#6 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(418): Illuminate\\Database\\Schema\\Builder->build()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 /home/<USER>/coffee-journal/database/migrations/2025_07_09_052427_create_coffee_notes_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php(32): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(440): Illuminate\\Database\\Connection->transaction()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#14 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending()
#19 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#23 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#27 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#29 /home/<USER>/coffee-journal/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute()
#30 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#31 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run()
#32 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#33 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#34 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#35 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle()
#36 /home/<USER>/coffee-journal/artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#37 {main}
"} 
[2025-07-09 05:25:29] local.ERROR: SQLSTATE[42P07]: Duplicate table: 7 ERROR:  relation "coffee_notes" already exists (Connection: pgsql, SQL: create table "coffee_notes" ("id" bigserial not null primary key, "bean_name" varchar(255) not null, "origin" varchar(255) null, "roast_level" varchar(255) not null, "flavor_notes" text null, "rating" integer null, "brewing_method" varchar(255) null, "created_at" timestamp(0) without time zone null, "updated_at" timestamp(0) without time zone null)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P07): SQLSTATE[42P07]: Duplicate table: 7 ERROR:  relation \"coffee_notes\" already exists (Connection: pgsql, SQL: create table \"coffee_notes\" (\"id\" bigserial not null primary key, \"bean_name\" varchar(255) not null, \"origin\" varchar(255) null, \"roast_level\" varchar(255) not null, \"flavor_notes\" text null, \"rating\" integer null, \"brewing_method\" varchar(255) null, \"created_at\" timestamp(0) without time zone null, \"updated_at\" timestamp(0) without time zone null)) at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(118): Illuminate\\Database\\Connection->statement()
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(418): Illuminate\\Database\\Schema\\Builder->build()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 /home/<USER>/coffee-journal/database/migrations/2025_07_09_052427_create_coffee_notes_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php(32): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(440): Illuminate\\Database\\Connection->transaction()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render()
#14 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#27 /home/<USER>/coffee-journal/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#29 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run()
#30 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#31 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#32 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#33 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 /home/<USER>/coffee-journal/artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#35 {main}

[previous exception] [object] (PDOException(code: 42P07): SQLSTATE[42P07]: Duplicate table: 7 ERROR:  relation \"coffee_notes\" already exists at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php:571)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(571): PDOStatement->execute()
#1 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(118): Illuminate\\Database\\Connection->statement()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build()
#6 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(418): Illuminate\\Database\\Schema\\Builder->build()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 /home/<USER>/coffee-journal/database/migrations/2025_07_09_052427_create_coffee_notes_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php(32): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(440): Illuminate\\Database\\Connection->transaction()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#14 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending()
#19 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#23 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#27 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#29 /home/<USER>/coffee-journal/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute()
#30 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#31 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run()
#32 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#33 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#34 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#35 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle()
#36 /home/<USER>/coffee-journal/artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#37 {main}
"} 
[2025-07-09 05:47:50] local.ERROR: The "--compact" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--compact\" option does not exist. at /home/<USER>/coffee-journal/vendor/symfony/console/Input/ArgvInput.php:226)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/symfony/console/Input/ArgvInput.php(155): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 /home/<USER>/coffee-journal/vendor/symfony/console/Input/ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 /home/<USER>/coffee-journal/vendor/symfony/console/Input/ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 /home/<USER>/coffee-journal/vendor/symfony/console/Input/Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 /home/<USER>/coffee-journal/vendor/symfony/console/Command/Command.php(276): Symfony\\Component\\Console\\Input\\Input->bind()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#6 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run()
#7 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#8 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#10 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 /home/<USER>/coffee-journal/artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#12 {main}
"} 
[2025-07-09 08:35:26] local.ERROR: SQLSTATE[23502]: Not null violation: 7 ERROR:  column "user_id" of relation "coffee_notes" contains null values (Connection: pgsql, SQL: alter table "coffee_notes" add column "user_id" bigint not null) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23502): SQLSTATE[23502]: Not null violation: 7 ERROR:  column \"user_id\" of relation \"coffee_notes\" contains null values (Connection: pgsql, SQL: alter table \"coffee_notes\" add column \"user_id\" bigint not null) at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(118): Illuminate\\Database\\Connection->statement()
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(406): Illuminate\\Database\\Schema\\Builder->build()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#6 /home/<USER>/coffee-journal/database/migrations/2025_07_09_083431_add_user_id_to_coffee_notes_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php(32): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(440): Illuminate\\Database\\Connection->transaction()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render()
#14 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#27 /home/<USER>/coffee-journal/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#29 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run()
#30 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#31 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#32 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#33 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 /home/<USER>/coffee-journal/artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#35 {main}

[previous exception] [object] (PDOException(code: 23502): SQLSTATE[23502]: Not null violation: 7 ERROR:  column \"user_id\" of relation \"coffee_notes\" contains null values at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php:571)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(571): PDOStatement->execute()
#1 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Connection.php(560): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(118): Illuminate\\Database\\Connection->statement()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build()
#6 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(406): Illuminate\\Database\\Schema\\Builder->build()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#8 /home/<USER>/coffee-journal/database/migrations/2025_07_09_083431_add_user_id_to_coffee_notes_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php(32): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(440): Illuminate\\Database\\Connection->transaction()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#14 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending()
#19 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#23 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#27 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Container/Container.php(696): Illuminate\\Container\\BoundMethod::call()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()
#29 /home/<USER>/coffee-journal/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute()
#30 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#31 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run()
#32 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#33 /home/<USER>/coffee-journal/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#34 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#35 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle()
#36 /home/<USER>/coffee-journal/artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#37 {main}
"} 
[2025-07-09 09:49:57] local.ERROR: Unable to locate file in Vite manifest: resources/js/Pages/Notes.vue. (View: /home/<USER>/coffee-journal/resources/views/app.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Unable to locate file in Vite manifest: resources/js/Pages/Notes.vue. (View: /home/<USER>/coffee-journal/resources/views/app.blade.php) at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Vite.php:987)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(192): Illuminate\\View\\View->getContents()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(161): Illuminate\\View\\View->renderContents()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(79): Illuminate\\View\\View->render()
#6 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(60): Illuminate\\Http\\Response->__construct()
#8 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(90): Illuminate\\Routing\\ResponseFactory->make()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#10 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Response.php(135): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(902): Inertia\\Response->toResponse()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(887): Illuminate\\Routing\\Router::toResponse()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#19 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Inertia\\Middleware->handle()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#27 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#29 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#31 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#32 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#34 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#36 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#38 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#39 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#40 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#41 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#42 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#43 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#46 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#49 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#51 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#53 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#55 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#57 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#59 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#61 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 /home/<USER>/coffee-journal/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#64 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteException(code: 0): Unable to locate file in Vite manifest: resources/js/Pages/Notes.vue. at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Vite.php:987)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Vite.php(390): Illuminate\\Foundation\\Vite->chunk()
#1 /home/<USER>/coffee-journal/storage/framework/views/2f5e6f8b612c78f6f4deb1bbb19a56ff.php(15): Illuminate\\Foundation\\Vite->__invoke()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(192): Illuminate\\View\\View->getContents()
#8 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(161): Illuminate\\View\\View->renderContents()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(79): Illuminate\\View\\View->render()
#10 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(60): Illuminate\\Http\\Response->__construct()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(90): Illuminate\\Routing\\ResponseFactory->make()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#14 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Response.php(135): Illuminate\\Support\\Facades\\Facade::__callStatic()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(902): Inertia\\Response->toResponse()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(887): Illuminate\\Routing\\Router::toResponse()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#19 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#23 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Inertia\\Middleware->handle()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#29 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#31 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#33 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#42 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#44 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#45 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#46 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#50 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#53 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#55 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#57 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#59 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#61 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#63 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#65 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 /home/<USER>/coffee-journal/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#68 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('...')
#69 {main}
"} 
[2025-07-09 09:52:39] local.ERROR: Unable to locate file in Vite manifest: resources/js/Pages/Notes.vue. (View: /home/<USER>/coffee-journal/resources/views/app.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Unable to locate file in Vite manifest: resources/js/Pages/Notes.vue. (View: /home/<USER>/coffee-journal/resources/views/app.blade.php) at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Vite.php:987)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(192): Illuminate\\View\\View->getContents()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(161): Illuminate\\View\\View->renderContents()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(79): Illuminate\\View\\View->render()
#6 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(60): Illuminate\\Http\\Response->__construct()
#8 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(90): Illuminate\\Routing\\ResponseFactory->make()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#10 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Response.php(135): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(902): Inertia\\Response->toResponse()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(887): Illuminate\\Routing\\Router::toResponse()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#19 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Inertia\\Middleware->handle()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#27 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#29 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#31 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#32 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#34 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#36 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#38 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#39 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#40 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#41 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#42 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#43 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#46 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#49 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#51 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#53 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#55 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#57 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#59 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#61 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 /home/<USER>/coffee-journal/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#64 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteException(code: 0): Unable to locate file in Vite manifest: resources/js/Pages/Notes.vue. at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Vite.php:987)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Vite.php(390): Illuminate\\Foundation\\Vite->chunk()
#1 /home/<USER>/coffee-journal/storage/framework/views/2f5e6f8b612c78f6f4deb1bbb19a56ff.php(15): Illuminate\\Foundation\\Vite->__invoke()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(192): Illuminate\\View\\View->getContents()
#8 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(161): Illuminate\\View\\View->renderContents()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(79): Illuminate\\View\\View->render()
#10 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(60): Illuminate\\Http\\Response->__construct()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(90): Illuminate\\Routing\\ResponseFactory->make()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#14 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Response.php(135): Illuminate\\Support\\Facades\\Facade::__callStatic()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(902): Inertia\\Response->toResponse()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(887): Illuminate\\Routing\\Router::toResponse()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#19 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#23 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Inertia\\Middleware->handle()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#29 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#31 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#33 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#42 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#44 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#45 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#46 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#50 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#53 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#55 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#57 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#59 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#61 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#63 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#65 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 /home/<USER>/coffee-journal/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#68 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('...')
#69 {main}
"} 
[2025-07-09 09:53:45] local.ERROR: Unable to locate file in Vite manifest: resources/js/Pages/Notes.vue. (View: /home/<USER>/coffee-journal/resources/views/app.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Unable to locate file in Vite manifest: resources/js/Pages/Notes.vue. (View: /home/<USER>/coffee-journal/resources/views/app.blade.php) at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Vite.php:987)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(192): Illuminate\\View\\View->getContents()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(161): Illuminate\\View\\View->renderContents()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(79): Illuminate\\View\\View->render()
#6 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(60): Illuminate\\Http\\Response->__construct()
#8 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(90): Illuminate\\Routing\\ResponseFactory->make()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#10 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Response.php(135): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(902): Inertia\\Response->toResponse()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(887): Illuminate\\Routing\\Router::toResponse()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#19 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Inertia\\Middleware->handle()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#27 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#29 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#31 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#32 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#34 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#36 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#38 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#39 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#40 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#41 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#42 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#43 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#46 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#49 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#51 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#53 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#55 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#57 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#59 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#61 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 /home/<USER>/coffee-journal/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#64 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('...')
#65 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteException(code: 0): Unable to locate file in Vite manifest: resources/js/Pages/Notes.vue. at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Vite.php:987)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Vite.php(390): Illuminate\\Foundation\\Vite->chunk()
#1 /home/<USER>/coffee-journal/storage/framework/views/2f5e6f8b612c78f6f4deb1bbb19a56ff.php(15): Illuminate\\Foundation\\Vite->__invoke()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(192): Illuminate\\View\\View->getContents()
#8 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/View.php(161): Illuminate\\View\\View->renderContents()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(79): Illuminate\\View\\View->render()
#10 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#11 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(60): Illuminate\\Http\\Response->__construct()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php(90): Illuminate\\Routing\\ResponseFactory->make()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Routing\\ResponseFactory->view()
#14 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Response.php(135): Illuminate\\Support\\Facades\\Facade::__callStatic()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(902): Inertia\\Response->toResponse()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(887): Illuminate\\Routing\\Router::toResponse()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#19 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#23 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Inertia\\Middleware->handle()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#29 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#31 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#33 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#42 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#44 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#45 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#46 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#50 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#53 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#55 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#57 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#59 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#61 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#63 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#65 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 /home/<USER>/coffee-journal/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#68 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('...')
#69 {main}
"} 
[2025-07-10 06:58:04] local.ERROR: Route [dashboard] not defined. {"userId":1,"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [dashboard] not defined. at /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:517)
[stacktrace]
#0 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(853): Illuminate\\Routing\\UrlGenerator->route()
#1 /home/<USER>/coffee-journal/app/Http/Controllers/Auth/AuthenticatedSessionController.php(36): route()
#2 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(47): App\\Http\\Controllers\\Auth\\AuthenticatedSessionController->store()
#3 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#4 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Route.php(212): Illuminate\\Routing\\Route->runController()
#5 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#6 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#7 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Auth/Middleware/RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#8 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle()
#9 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#10 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#11 /home/<USER>/coffee-journal/vendor/inertiajs/inertia-laravel/src/Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Inertia\\Middleware->handle()
#13 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#15 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#17 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#19 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#21 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle()
#22 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#24 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#26 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#28 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#29 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#30 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#31 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#32 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#33 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#36 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#39 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#41 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#43 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#45 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#47 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#49 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#51 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 /home/<USER>/coffee-journal/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#54 /home/<USER>/coffee-journal/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('...')
#55 {main}
"} 
