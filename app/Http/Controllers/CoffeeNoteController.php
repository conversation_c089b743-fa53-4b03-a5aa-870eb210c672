<?php

namespace App\Http\Controllers;

use App\Models\CoffeeNote;
use Illuminate\Http\Request;

class CoffeeNoteController extends Controller
{
    public function index(Request $request)
    {
        return $request->user()->notes()->latest()->get();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'bean_name' => 'required|string|max:255',
            'origin' => 'nullable|string|max:255',
            'roast_level' => 'required|string|max:255',
            'flavor_notes' => 'nullable|string',
            'rating' => 'nullable|integer|min:1|max:5',
            'brewing_method' => 'nullable|string|max:255',
        ]);
        // 使用 authenticated user 的 notes 關聯來創建新的 CoffeeNote，確保每個 CoffeeNote 都與當前使用者相關
        // 這樣可以在 CoffeeNote 中存取對應的 User 資料，例如：$coffeeNote->user->name
        return $request->user()->notes()->create($validated); 
    }

    public function show($id)
    {
        return CoffeeNote::findOrFail($id);
    }

    public function update(Request $request, $id)
    {
        $note = $request->user()->notes()->findOrFail($id);
        $validated = $request->validate([
            'bean_name' => 'required|string|max:255',
            'origin' => 'nullable|string|max:255',
            'roast_level' => 'required|string|max:255',
            'flavor_notes' => 'nullable|string',
            'rating' => 'nullable|integer|min:1|max:5',
            'brewing_method' => 'nullable|string|max:255',
        ]);
        $note->update($validated);
        return $note;
    }

    public function destroy(Request $request, $id)
    {
        $note = $request->user()->notes()->findOrFail($id);
        $note->delete();
        return response()->json(['message' => 'deleted']);
    }
}
