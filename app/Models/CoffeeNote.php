<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CoffeeNote extends Model
{
    //定義模型的屬性
    //這些屬性對應到資料庫中的欄位
    //這樣可以在 CoffeeNote 模型中使用這些屬性
    protected $table = 'coffee_notes'; // 指定資料表名稱
    protected $primaryKey = 'id'; // 指定主鍵欄位
    public $timestamps = true; // 啟用時間戳記          
    protected $fillable =
    [
    'bean_name',
    'origin',
    'roast_level',
    'flavor_notes',
    'rating',
    'brewing_method',
    'user_id',
    ];

    // 定義與 User 模型的關聯
    // 每個 CoffeeNote 都屬於一個 User
    // 這樣可以在 CoffeeNote 中存取對應的 User 資料
    // 例如：$coffeeNote->user->name
    public function user()
    {
        return $this->belongsTo(User::class);// 指定關聯的模型
    }
}
