version: '3.8'

services:
  # PostgreSQL 資料庫
  postgres:
    image: postgres:15-alpine
    container_name: coffeenote-postgres
    environment:
      POSTGRES_DB: coffeenote
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - coffeenote-network
    restart: unless-stopped

  # Java Spring Boot 後端
  backend-java:
    build:
      context: ../backend-java
      dockerfile: Dockerfile
    container_name: coffeenote-java-api
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ******************************************
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: postgres123
      JWT_SECRET: mySecretKey12345678901234567890123456789012345678901234567890
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - coffeenote-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Laravel + Vue 前端
  backend-laravel:
    build:
      context: ../backend-laravel
      dockerfile: Dockerfile
    container_name: coffeenote-laravel-frontend
    environment:
      APP_ENV: production
      APP_DEBUG: false
      APP_URL: http://localhost:8000
      DB_CONNECTION: pgsql
      DB_HOST: postgres
      DB_PORT: 5432
      DB_DATABASE: coffeenote
      DB_USERNAME: postgres
      DB_PASSWORD: postgres123
      JAVA_API_URL: http://backend-java:8080
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - backend-java
    networks:
      - coffeenote-network
    restart: unless-stopped
    volumes:
      - laravel_storage:/var/www/html/storage

  # Nginx 反向代理（可選）
  nginx:
    image: nginx:alpine
    container_name: coffeenote-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend-laravel
      - backend-java
    networks:
      - coffeenote-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  laravel_storage:
    driver: local

networks:
  coffeenote-network:
    driver: bridge
